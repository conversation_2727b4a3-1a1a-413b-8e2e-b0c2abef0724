import { AppStateActions, AudioSynthActions } from "~/proto/pianorhythm-actions";
import { ClientSideUserDto } from "~/proto/user-renditions";
import { WasmSynth } from "./audio.types";

export interface ITauriService {
  listenEvents(
    onHandleAppEvent: (evt: number) => void,
    onHandleAppEffects: (evt: Uint8Array) => void
  ): Promise<void>;

  createCoreService(
    createAppAction: (action: AppStateActions) => Uint8Array,
    on_emit_core_app_actions?: (bytes: Uint8Array) => void
  ): CoreService;
}

export interface ICoreWasmService {
  initWithPayload(payload: any): Promise<void>;

  getWasmInstance(): any | null;

  createCoreWasmWebNonWebWorker(
    onHandleAppEvent: (event: number) => void,
    onHandleAppEffects: (bytes: Uint8Array) => void,
    handle_core_app_actions: (action: any) => void
  ): Promise<CoreService>;
}

/**
 * Represents the CoreService interface.
 * This interface extends the WasmSynth interface and defines additional methods and properties.
 */
export interface CoreService extends WasmSynth {
  create_synth(synth_engine_idx: number, _sampleRate: number): void;

  create_synth_stream(sampleRate: number): void;

  reset_app_state(): void;

  midi_io_start(ctx: AudioContext, channels: number): void;

  init_note_buffer_engine(): void;

  flush_note_buffer_engine(): void;

  note_buffer_engine_set_self_hosted(is_self_hosted: boolean): void;

  update_server_timing(ping_time: BigInt, server_time: BigInt, local_time: BigInt): void;

  init_wasm(is_renderer_webworker?: boolean): void;

  get_core_version(): Promise<string>;

  get_synth_version(): Promise<string>;

  get_renderer_version(): Promise<string>;

  get_hash_socket_id(socket_id: string): Promise<number | undefined>;

  websocket_connect(url: string, on_connect?: () => void, on_error?: () => void, on_close?: () => void): Promise<void>;

  websocket_send_binary(bytes: Uint8Array): void;

  websocket_disconnect(): void;

  core_to_renderer_effects(bytes: Uint8Array): void;

  core_to_renderer_events(bytes: Uint32Array): void;

  emit_core_app_actions(bytes: Uint8Array): void;

  synth_load_soundfont(bytes: Uint8Array): void;

  send_app_action(action: AppStateActions, toRendererWorker?: boolean): void;

  send_app_action_bytes(action: Uint8Array): void;

  send_app_synth_action(action: AudioSynthActions): void;

  synth_sustain(value: number, socket_id?: string): void;

  webrtc_connect: (roomID: string) => void;
  webrtc_disconnect: () => void;
}

export namespace CoreService {
  export const DEFAULT: CoreService = {
    ...WasmSynth.DEFAULT,
    reset_app_state: () => {
    },
    flush_note_buffer_engine: () => {
    },
    init_note_buffer_engine: () => {
    },
    init_wasm: () => {
    },
    get_core_version: async () => "",
    get_synth_version: async () => "",
    get_renderer_version: async () => "",
    get_hash_socket_id: () => Promise.resolve(undefined),
    websocket_connect: async () => {
    },
    websocket_send_binary: () => {
    },
    websocket_disconnect: () => {
    },
    emit_core_app_actions: () => {
    },
    synth_load_soundfont: () => {
    },
    send_app_action: () => {
    },
    send_app_synth_action: () => {
    },
    synth_sustain: () => {
    },
    send_app_action_bytes: function (action: Uint8Array): void {
    },
    create_synth: function (): void {
    },
    create_synth_stream: function (sampleRate: number): void {
    },
    midi_io_start: function (ctx: AudioContext, channels: number): void {
    },
    webrtc_connect: function (roomID: string): void {
    },
    webrtc_disconnect: function (): void {
    },
    note_buffer_engine_set_self_hosted: function (is_self_hosted: boolean): void {
    },
    update_server_timing: function (ping_time: number, server_time: number, local_time: number): void {
    },
    core_to_renderer_effects: function (bytes: Uint8Array): void {
    },
    core_to_renderer_events: function (bytes: Uint32Array): void {
    }
  };
}

export enum GameMode {
  NONE,
  SOLO_TRAINING_MAIN_MENU,
  EAR_TRAINING,
  MANIA,
  MANIA_BEAT_EDITOR,
}

export enum GameModeType {
  UNKNOWN,
  PITCH_PERFECT,
  SCALES,
}

export type GameModeState = {
  mode: GameMode;
  type?: GameModeType;
};

export namespace GameModeState {
  export const DEFAULT = (mode = GameMode.NONE) => ({
    mode: mode,
    type: undefined
  });

  export const New = (mode: GameMode, type?: GameModeType): GameModeState => ({
    mode, type
  });
}

/**
 * Represents the scene mode for the Piano Rhythm application.
 */
export enum PianoRhythmSceneMode {
  THREE_D,
  TWO_D,
  GAME_MODE,
  WORLD_MODE,
  UNKNOWN
}

export namespace PianoRhythmSceneMode {
  export const ModeToString = (mode: PianoRhythmSceneMode): string => {
    switch (mode) {
      case PianoRhythmSceneMode.THREE_D:
        return "3D";
      case PianoRhythmSceneMode.TWO_D:
        return "2D";
      case PianoRhythmSceneMode.GAME_MODE:
        return "GAME";
      case PianoRhythmSceneMode.WORLD_MODE:
        return "WORLD";
      default:
        return "Unknown";
    }
  };
}

/**
 * Represents the current page in the application.
 */
export enum CurrentPage {
  Home,
  Room,
  RoomLoading,
  AppLoading,
}

export type JoinRoomResponse =
  | "Success"
  | "Failed"
  | "EnterPassword";

export type KeyboardLayout = "MPP" | "VP" | "CUSTOM";

export type CustomKeyboardKeyMap = {
  keyCode: number;
  note: number;
};

export type AppEvents =
  | "ClientWelcome"
  | [type: "JoinedRoom", roomID: string]
  | [type: "SetRoomOwner", roomOwner?: string]
  // | [type: "AvatarCreatorUpdate", data: WorldCharacterUserDataDto]
  | [type: "AvatarCreatorSetCameraTarget", target: string]
  | [type: "AvatarCreatorSetAnimation", animation: string]
  | [type: "UserUpdated", target: ClientSideUserDto]
  // | [type: "OrchestraModelUpdated", target: OrchestraModel]
  // | [type: "OrchestraModelSetDecal", target: OrchestraModelDecal]
  | [type: "OrchestraModelUpdateDecalSize", target: number]
  | [type: "OrchestraModelUpdateDecalAngle", target: number]
  | [type: "OrchestraModelUpdateDecalColor", target: string]
  // | [type: "OrchestraModelDecalPlaced", target: OrchestraModelDecalDetail]
  // | [type: "OrchestraModelDecalRemoved", target: OrchestraModelDecalDetail]
  | [type: "OrchestraModelEditMode", target: boolean]
  | [type: "SendAppCoreActionToWorker", target: Uint8Array]
  | "OrchestraModelReset"
  | "GoToRoomLoadingPage"
  | "Disconnected"
  | "Disposed";

/**
 * Represents the available themes for the application.
 */
export enum AppThemes {
  DEFAULT,
  THEME_1,
  THEME_2,
  THEME_3,
  THEME_4,
  THEME_5,
  THEME_6,
  WHITE_AND_BLACK,
  BLACK_AND_WHITE,
  HALLOWEEN,
}

export enum ROOT_FOLDER_LOCATION {
  UI = "UI",
  UI_2 = "UI_2",
  FX = "FX",
  BG_MUSIC = "BG_MUSIC",
  STAGE_AUDIO_FX = "STAGE_AUDIO_FX",
}

export type SoundPlayOptions = {
  loop?: boolean;
  autoPlay?: boolean;
  volume?: number;
  rate?: number;
  pan?: number;
  rootFolder?: ROOT_FOLDER_LOCATION;
  fadeIn?: boolean;
  autoUnload?: boolean;
  onstop?: () => void;
  onplay?: () => void;
  onend?: () => void;
  onload?: () => void;
};

export type SoundPlayOutput = {
  //TODO: Import Howler type
  howler: any;
  stop: () => void;
  destroy: () => void;
  fadeIn: (value?: number, duration?: number) => void;
  fadeOut: (duration?: number, initialVolume?: number, autoUnload?: boolean) => void;
};

/**
 * Enum representing the sound effects used in the application.
 */
export enum AppSoundFx {
  CLICK = 'CLICK',
  CLICK2 = 'CLICK2',
  CLICK_ERROR = 'CLICK_ERROR',
  HOVER = 'HOVER',
  CONFIRM = 'CONFIRM',
  CANCEL = 'CANCEL',
  CANCEL2 = 'CANCEL2',
  ERROR = 'ERROR',
  POPUP_OPEN = 'POPUP_OPEN',
  POPUP_CLOSE = 'POPUP_CLOSE',
  POPUP_OPEN2 = 'POPUP_OPEN2',
  POPUP_CLOSE2 = 'POPUP_CLOSE2',
  POPUP_OPEN3 = 'POPUP_OPEN3',
  POPUP_CLOSE3 = 'POPUP_CLOSE3',
  SELECT = 'SELECT',
  SELECT_ALL_TEXT = "SELECT_ALL_TEXT",
  INPUT_TYPE = 'INPUT_TYPE',
  SUCCESS = 'SUCCESS',
  WARNING = "WARNING",
  NOTIFICATION_SUCCESS = 'NOTIFICATION_SUCCESS',
  NOTIFICATION_INFO = 'NOTIFICATION_INFO',
  WOOSH_DOWN = 'WOOSH_DOWN',
  WOOSH_UP = 'WOOSH_UP',
  POP = "POP",
  ARCADE_JUMP = "ARCADE_JUMP",
  TONE_POSITIVE = "TONE_POSITIVE",
  TONE_NEGATIVE = "TONE_NEGATIVE",
  LOGO_TEXT_INTRO = "LOGO_TEXT_INTRO",
  WELCOME = "WELCOME",
  GOODBYE = "GOODBYE"
}

export enum AppSoundFx2 {
  CANCEL = "CANCEL",
  CLOSE_MENU = "CLOSE_MENU",
  CONFIRM = "CONFIRM",
  EQUIP = "EQUIP",
  EXIT = "EXIT",
  MENU_SELECTION = "MENU_SELECTION",
  OPEN_MENU = "OPEN_MENU",
  PAUSE = "PAUSE",
  RESUME = "RESUME",
  SAVED = "SAVED",
  SHOP = "SHOP",
  UNEQUIP = "UNEQUIP",
}

export enum StageAudioSfx {
  GENTLE_BREEZE_1 = "GENTLE_BREEZE_1",
  SOFT_RAIN_1 = "SOFT_RAIN_1",
  BIRDS_JUNGLE_1 = "BIRDS_JUNGLE_1",
}

export enum OtherSoundFx {
  WOOSH_1 = "WOOSH_1"
}

export type SystemsSummary = {
  page: {
    name: string;
    url: string;
  };
  components: any[];
  incidents: any[];
  scheduled_maintenances: {
    name: string;
    status: "scheduled" | "in_progress" | "completed";
    impact: string;
    scheduled_for: string;
    scheduled_until: string;
    started_at: string;
    updated_at: string;
    resolved_at: string | null;
  }[];
  status: {
    indicator: string;
    description: string;
  };
};
