import { createEventBus } from "@solid-primitives/event-bus";
import debounce from "lodash-es/debounce";
import { Subject } from 'rxjs';
import { createMemo, createSignal } from "solid-js";
import toast from "solid-toast";
import * as webWorkerProxy from 'web-worker-proxy';
import { JoinRoomFailType, WelcomeDto } from "~/proto/client-message";
import { AppStateActions, AppStateActions_Action, AudioSynthActions } from "~/proto/pianorhythm-actions";
import { AppStateEffects, AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import { AppStateEvents, appStateEventsFromJSON } from "~/proto/pianorhythm-events";
import { RoomSettings, RoomStageDetails, RoomType } from "~/proto/room-renditions";
import { CreateRoomParam } from "~/proto/server-message";
import { Roles, UserClientDto, UserStatus } from "~/proto/user-renditions";
import { CoreService, CurrentPage, ICoreWasmService, ITauriService, PianoRhythmSceneMode } from "~/types/app.types";
import { PianoRhythmSynthEvent, SynthInterpolationMethod, WasmSynth } from "~/types/audio.types";
import { DEFAULT_CREATE_ROOM_PARAM, RoomStagesNS } from '~/types/room.types';
import { UserClientDomain, UserDtoUtil } from "~/types/user-helper";
import { DEFAULT_UserNotificationSettings, RoomID, UserNotificationSettings } from "~/types/user.types";
import { CHANNELS, COMMON, IDS } from "~/util/const.common";
import { canCreateSharedArrayBuffer } from '~/util/helpers';
import { logInfo } from "~/util/logger";

/**
 * Represents the AppService class.
 * This class provides various methods and properties for managing the application state and functionality.
 * It handles client data, room information, themes, notifications, and more.
 */
export default function AppService() {
  const [clientLoaded, _setClientLoaded] = createSignal(false);
  const [sceneMode, setSceneMode] = createSignal(PianoRhythmSceneMode.THREE_D);
  const [client, _setClient] = createSignal<UserClientDomain>(new UserClientDomain());
  const [canvasWorker, setCanvasWorker] = createSignal<Worker>();
  const [roomID, setRoomID] = createSignal<string>();
  const [currentPage, setCurrentPage] = createSignal<CurrentPage>(CurrentPage.Home);
  const [maintenanceModeActive, setMaintenanceModeActive] = createSignal(false);
  const [coreService, setCoreService] = createSignal<CoreService>();
  const [canvasLoaded, _setCanvasLoaded] = createSignal(true);
  const [firstTimeJoinRoom, setFirstTimeJoinRoom] = createSignal(true);
  const [coreWasmLoaded, setCoreWasmLoaded] = createSignal(false);
  const [initialized, setInitialized] = createSignal(false);
  const [activatePageLoader, setActivatePageLoader] = createSignal(false);
  const [pagerLoaderAnimating, setPagerLoaderAnimating] = createSignal(false);
  const [activePageLoaderToolTip, setActivePageLoaderToolTip] = createSignal("");
  const [welcomeEvent, setWelcomeEvent] = createSignal<WelcomeDto>();
  const [serverServiceDown, setServerServiceDown] = createSignal(false);
  const [clientIsSelfNotesMuted, setClientIsSelfNotesMuted] = createSignal(false);
  const [clientIsSelfChatMuted, setClientIsSelfChatMuted] = createSignal(false);
  const [userNotificationSettings, setUserNotificationSettings] = createSignal<UserNotificationSettings>(DEFAULT_UserNotificationSettings);
  const [appVersionMismatched, setAppVersionMismatched] = createSignal(false);
  const [roomOwner, setRoomOwner] = createSignal<string>();
  const [roomName, setRoomName] = createSignal<string>();
  const [queuedEffectsForRendererConsumed, setQueuedEffectsForRendererConsumed] = createSignal(false);
  const [queuedEffectsForRenderer, setQueuedEffectsForRenderer] = createSignal<Uint8Array[]>([]);
  const [queuedEventsForRenderer, setQueuedEventsForRenderer] = createSignal<Uint32Array[]>([]);
  const [roomType, setRoomType] = createSignal<RoomType>();
  const [roomSettings, _setRoomSettings] = createSignal<RoomSettings>();
  const [clientHasEveryoneElseMuted, _setClientHasEveryoneElseMuted] = createSignal(false);
  const [currentRoomParam, setCurrentRoomParam] = createSignal<CreateRoomParam>();

  /**
   * Event bus that triggers when a disconnect event occurs.
   * This event bus does not carry any payload.
   */
  const onDisconnectEvents = createEventBus<void>();

  /**
   * Represents an event bus for the app state events.
   */
  const appStateEvents = createEventBus<AppStateEvents>();

  /**
   * Creates an event bus for the AppStateEffects.
   *
   * @param {AppStateEffects} appStateEffects - The AppStateEffects instance.
   * @returns {EventBus<AppStateEffects>} - The event bus for the AppStateEffects.
   */
  const appStateEffects = createEventBus<AppStateEffects>();

  /**
   * Event bus for WASM MIDI sequencer effects data.
   * Handles binary data communication for MIDI effect processing.
   * @type {EventBus<Uint8Array>}
   */
  const wasmMidiSequencerEffects = createEventBus<Uint8Array>();

  /**
   * Event bus for WebMIDI connection events.
   * Emits events when MIDI inputs or outputs are connected or disconnected.
   *
   * @event
   * @property {string} id - The unique identifier of the MIDI device
   * @property {boolean} active - The connection status of the MIDI device
   * @property {'input' | 'output'} type - Whether the device is an input or output
   */
  const webMidiConnectionEvents = createEventBus<{
    id: string,
    name: string,
    active: boolean,
    type: "input" | "output";
  }>();

  /**
   * Represents a communication channel between the core and renderer.
   */
  let webCore2RendererChannel: BroadcastChannel | undefined;
  let userGestureController = new AbortController();

  const desktopSynthEvents = new Subject<PianoRhythmSynthEvent>();

  const canInteract = createMemo(() => {
    return clientLoaded() && coreWasmLoaded() && initialized();
  });

  const setClient = (client: UserClientDomain) => {
    _setClient(client);
  };

  const onSetRoomOwner = (roomOwner?: string) => {
    setRoomOwner(roomOwner);
  };

  /**
   * Checks if the current page is the room page.
   *
   * @returns True if the current page is the room page, otherwise false.
   */
  const isRoomCurrentPage = createMemo(() => currentPage() == CurrentPage.Room);

  /**
   * Checks if the client is the room owner.
   *
   * @returns True if the client is the room owner, otherwise false.
   */
  const isClientRoomOwner = createMemo(() => client()?.socketID?.toLowerCase() == roomOwner()?.toLowerCase());

  /**
   * Checks if a user with the given socket ID is the room owner.
   *
   * @param socketID - The socket ID of the user.
   * @returns True if the user with the given socket ID is the room owner, otherwise false.
   */
  const isUserRoomOwner = ((socketID?: string) => socketID?.toLowerCase() == roomOwner()?.toLowerCase());

  /**
   * Checks if the client is a member.
   *
   * @returns True if the client is a member, otherwise false.
   */
  const isClientMember = createMemo(() => client().isMember);

  /**
   * Checks if the client is a moderator.
   *
   * @returns True if the client is a moderator, otherwise false.
   */
  const isClientMod = createMemo(() => client().isMod);

  /**
   * Checks if the client is an administrator.
   *
   * @returns True if the client is an administrator, otherwise false.
   */
  const isClientAdmin = createMemo(() => client().isAdmin);

  /**
   * Checks if the client is a pro member.
   *
   * @returns True if the client is a pro member, otherwise false.
   */
  const isClientProMember = createMemo(() => client().isProMember);

  /**
   * Retrieves the status of the client.
   *
   * @returns The status of the client.
   */
  const getClientStatus = createMemo(() => client().status);

  /**
   * Retrieves the user tag of the client.
   *
   * @returns The user tag of the client.
   */
  const getUserTag = createMemo(() => client().usertag);

  /**
   * Retrieves the meta details of the client.
   *
   * @returns The meta details of the client.
   */
  const getClientMetaDetails = createMemo(() => client().getClientMetaDetails());

  /**
   * Retrieves the socket ID from the client.
   *
   * @returns The socket ID.
   */
  const getSocketID = createMemo(() => client().socketID);

  /**
   * Determines if the client is in "Do Not Disturb" status.
   *
   * @returns {boolean} True if the client is in "Do Not Disturb" status, false otherwise.
   */
  const isClientInDoNotDisturb = createMemo(() => client().status == UserStatus.DoNotDisturb);

  const isClientServerNotesMuted = createMemo(() => client().serverNotesMuted);

  const doesClientHaveSheetMusicFullAccess = createMemo(() => isClientMod() || UserDtoUtil.hasRole(client().getRoles() as Roles[], Roles.SHEETMUSICEDITOR));

  const doesClientHaveMidiMusicFullAccess = createMemo(() => isClientMod() || UserDtoUtil.hasRole(client().getRoles() as Roles[], Roles.MIDIMUSICEDITOR));

  const isClientInRoom = (_roomID: RoomID) => _roomID == roomID();

  const canReport = createMemo(() => isClientMember());

  /**
   * Checks if the provided socket ID matches the client's socket ID.
   *
   * @param socketID - The socket ID to compare with the client's socket ID.
   * @returns A boolean indicating whether the socket ID matches the client's socket ID.
   */
  const isClient = (socketID: string) => client().socketID == socketID;

  /**
   * Encodes the given AppStateEffects object.
   *
   * @param effect - The AppStateEffects object to encode.
   * @returns The encoded AppStateEffects object.
   */
  const encodeAppEffect = (effect: AppStateEffects) => {
    return AppStateEffects.encode(effect).finish();
  };

  /**
   * Decodes the given bytes into an AppStateEffects object.
   *
   * @param bytes - The bytes to decode.
   * @returns The decoded AppStateEffects object.
   */
  const decodeAppEffect = (bytes: Uint8Array) => {
    return AppStateEffects.decode(bytes);
  };

  /**
   * Handles the application effects based on the given bytes.
   * @param bytes - The Uint8Array representing the effects.
   */
  const onHandleAppEffects = (bytes: Uint8Array) => {
    const effect = decodeAppEffect(bytes);
    if (!AppStateEffects_Action[effect.action]) return;
    if (COMMON.IS_DEV_MODE || COMMON.IS_STAGING) console.log("effect", AppStateEffects_Action[effect.action], effect);

    switch (effect.action) {
      case AppStateEffects_Action.OnWelcome: {
        if (!effect.welcomeDto) return;
        setWelcomeEvent(effect.welcomeDto);
        onClientLoaded(effect.welcomeDto?.userClientDto);
        break;
      }
      case AppStateEffects_Action.ClientUpdated: {
        if (!effect.userClientDto) return;
        onClientLoaded(effect.userClientDto);
        break;
      }
      case AppStateEffects_Action.SetPageLoaderDetails: {
        if (!effect.appPageLoaderDetail) return;

        setActivatePageLoader(effect.appPageLoaderDetail.active);
        setActivePageLoaderToolTip(effect.appPageLoaderDetail.details ?? "");

        // clearTimeout(pageLoaderDetailsTimeout);
        // pageLoaderDetailsTimeout = window.setTimeout(() => setActivatePageLoader(false), 3000);
        break;
      }
      case AppStateEffects_Action.SetRoomSettings: {
        if (!effect.roomSettings) return;
        _setRoomSettings(effect.roomSettings);
        break;
      }
      case AppStateEffects_Action.SetRoomOwner: {
        if (!effect.socketId) return;
        onSetRoomOwner(effect.socketId);
        break;
      }
      case AppStateEffects_Action.JoinedRoomSuccess: {
        let dto = effect.joinedRoomData;
        if (!dto) return;

        setFirstTimeJoinRoom(false);
        let _roomID = dto.roomID;
        onSetRoomOwner(dto.roomOwner);
        _setRoomSettings(dto.roomSettings);
        setRoomName(dto.roomName);
        setRoomType(dto.roomType);
        setRoomID(_roomID);

        break;
      }
      case AppStateEffects_Action.SetRoomFullDetails: {
        if (!effect.roomFullDetails) return;
        let details = effect.roomFullDetails;
        setRoomName(details.roomName);
        setRoomID(details.roomID);
        _setRoomSettings(details.settings);
        break;
      }
      case AppStateEffects_Action.JoinRoomFailResponse: {
        let response = effect.joinRoomFailResponse;
        let reason = response?.reason ?? 8;
        if (reason == JoinRoomFailType.EnterPassword) break;

        // NotificationService.show({
        //   type: "danger",
        //   title: "PianoRhythm",
        //   description: `
        //     Failed to join room: ${response?.roomName || response?.roomID}
        //     \n${joinRoomFailTypeToMessage(joinRoomFailTypeFromJSON(reason))}
        //     `,
        //   duration: 5000
        // });

        // Handle edge case where user fails to join the first time
        // due to a server error. Disconnect websocket.
        if (firstTimeJoinRoom() && reason === JoinRoomFailType.ServerError) {
          onDisconnect();
        }
        break;
      }
    }

    appStateEffects.emit(effect);

    webCore2RendererChannel?.postMessage({ event: "app_effects", data: bytes });
    if (!queuedEffectsForRendererConsumed()) {
      setQueuedEffectsForRenderer([...queuedEffectsForRenderer(), bytes]);
    }
  };

  /**
   * Handles an application event.
   *
   * @param byte - The byte representing the event.
   * @returns The event that was handled.
   */
  const onHandleAppEvent = (byte: number) => {
    let event = appStateEventsFromJSON(byte);
    if (COMMON.IS_DEV_MODE) console.log("event:", AppStateEvents[event]);

    switch (event) {
      // The renderer (PianoRhythm Renderer) dispatches this event
      // when the room stage is loaded.
      case AppStateEvents.RoomStageLoaded: {
        setActivatePageLoader(false);
        break;
      }
      default: {
        break;
      }
    }

    appStateEvents.emit(event);
    webCore2RendererChannel?.postMessage({ event: "app_events", data: [byte] });

    if (!queuedEffectsForRendererConsumed()) {
      setQueuedEventsForRenderer([...queuedEventsForRenderer(), [byte] as any]);
    }

    return event;
  };

  /**
   * Handles the logic when the client is loaded.
   *
   * @param userClientDto - The user client data transfer object.
   */
  const onClientLoaded = (userClientDto?: UserClientDto) => {
    if (!userClientDto) return;

    if (!clientLoaded()) {
      setClient(new UserClientDomain(userClientDto));
      _setClientLoaded(true);
    } else {
      _setClient(prev => prev.updateClient(userClientDto));
    }
  };

  /**
   * Sends an initialize app state action to the core service.
   * This action initializes the app state and sets the user as logged in.
   */
  const sendInitializeAppState = () => {
    coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.InitializeAppState
    }));

    coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.SetLoggedIn,
      boolValue: true
    }));
  };

  /**
   * Creates an app action.
   *
   * @param action - The app state action.
   * @returns The encoded app action.
   */
  const createAppAction = (action: AppStateActions) => {
    let appAction = AppStateActions.create(action);
    return AppStateActions.encode(appAction).finish();
  };

  let handle_core_app_actions = (action: AppStateActions) => {
    let actionBytes = createAppAction(action);
    coreService()?.emit_core_app_actions(actionBytes);
    sendAppActionsToRenderer(actionBytes);
  };

  const beforeUnload = (event: BeforeUnloadEvent) => {
    if (initialized() && COMMON.IS_WEB_APP) {
      return event.returnValue = `Are you sure you want to leave?`;
    }

    // Handles proper cleanup during hot reloading
    if (COMMON.IS_DESKTOP_APP) onDisconnect();
  };

  const loadCoreWasmWebNonWebWorker = async (coreWasmService: ICoreWasmService) => {
    let coreService = await coreWasmService.createCoreWasmWebNonWebWorker(
      onHandleAppEvent,
      onHandleAppEffects,
      handle_core_app_actions
    );
    setCoreService(coreService);
    sendInitializeAppState();
    setCoreWasmLoaded(true);
  };

  /**
   * Loads the core WebAssembly module and initializes the core service.
   * @returns {Promise<void>} A promise that resolves once the core WebAssembly module is loaded and the core service is initialized.
   */
  const loadCoreWasmWeb = (coreWasmService: ICoreWasmService): Promise<void> => {
    return new Promise(async (resolve, reject) => {
      const worker = new (await import('~/workers/app.worker.ts?worker')).default({
        name: "app-worker"
      });

      worker.onmessage = async (event) => {
        let data = event.data;

        if (data.event === "app_effects") {
          onHandleAppEffects(data.data);
        }

        if (data.event === "app_events") {
          onHandleAppEvent(data.data);
        }

        if (data.event === "wasm_midi_sequencer_effects") {
          wasmMidiSequencerEffects.emit(data.payload);
        }

        if (data.event === "wasm-module-loaded") {
          let payload = data.payload;
          if (COMMON.IS_DEV_MODE) console.log("Wasm Module Loaded", payload);

          window.addEventListener("app_events", (event) => {
            let data = ((event as any).detail as Array<number>);
            data.forEach(onHandleAppEvent);
          });

          window.addEventListener("app_effects", (event) => {
            let bytes = ((event as any).detail as Uint8Array);
            onHandleAppEffects(bytes);
          });

          await coreWasmService.initWithPayload(payload);
          let core_wasm = coreWasmService.getWasmInstance();

          let proxy = webWorkerProxy.create(worker);
          setCoreService({
            ...CoreService.DEFAULT,
            ...WasmSynth.DEFAULT,
            create_synth: (synth_engine, sample_rate) => {
              let desc = new core_wasm.PianoRhythmSynthesizerDescriptor(synth_engine, sample_rate);
              core_wasm.create_synth(desc);
            },
            get_wasm_module: async () => {
              return await proxy.get_wasm_module();
            },
            get_wasm_memory: async () => {
              return await proxy.get_wasm_memory();
            },
            set_interpolation_method: (_interpolation_method) => {
              let method = SynthInterpolationMethod.ToID(_interpolation_method);
              proxy.set_interpolation_method(method);
            },
            instrument_exists: (bank, program) => Promise.resolve(core_wasm.instrument_exists(bank, program)),
            get_synth_users: () => Promise.resolve(core_wasm.get_synth_users()),
            get_program: (channel: number, socket_id?: number) => Promise.resolve(core_wasm.get_program(channel, socket_id)),
            synth_get_reverb_room_size: () => Promise.resolve(core_wasm.synth_get_reverb_room_size()),
            synth_get_reverb_level: () => Promise.resolve(core_wasm.synth_get_reverb_level()),
            synth_get_reverb_damp: () => Promise.resolve(core_wasm.synth_get_reverb_damp()),
            synth_get_reverb_width: () => Promise.resolve(core_wasm.synth_get_reverb_width()),
            create_synth_stream: core_wasm.create_synth_stream,
            midi_io_start: core_wasm.midi_io_start,
            update_server_timing: core_wasm.update_server_timing,
            webrtc_connect: core_wasm.webrtc_connect,
            webrtc_disconnect: core_wasm.webrtc_disconnect,
            reset_app_state: proxy.reset_app_state,
            flush_note_buffer_engine: proxy.flush_note_buffer_engine,
            init_note_buffer_engine: proxy.init_note_buffer_engine,
            init_wasm: proxy.init_wasm,
            get_core_version: proxy.get_core_version,
            get_synth_version: proxy.get_synth_version,
            get_renderer_version: proxy.get_renderer_version,
            get_hash_socket_id: proxy.get_hash_socket_id,
            websocket_connect: proxy.websocket_connect,
            websocket_send_binary: proxy.websocket_send_binary,
            websocket_disconnect: proxy.websocket_disconnect,
            emit_core_app_actions: proxy.emit_core_app_actions,
            synth_load_soundfont: proxy.synth_load_soundfont,
            send_app_action: handle_core_app_actions,
            send_app_synth_action: (action: AudioSynthActions) => {
              handle_core_app_actions(AppStateActions.create({
                action: AppStateActions_Action.SynthAction,
                audioSynthAction: AudioSynthActions.create(action)
              }));
            },
            synth_sustain: proxy.synth_sustain,
            send_app_action_bytes: function (action: Uint8Array) {
              handle_core_app_actions(AppStateActions.decode(action));
            },
            on_load_renderer_wasm_module: proxy.on_load_renderer_wasm_module,
            hash_device_id: proxy.hash_device_id,
            note_buffer_engine_set_self_hosted: proxy.note_buffer_engine_set_self_hosted,
            set_max_multi_mode_channels: proxy.set_max_multi_mode_channels,
            get_all_presets_from_sf: proxy.get_all_presets_from_sf,
            set_channel_volume: proxy.set_channel_volume,
            set_max_velocity: proxy.set_max_velocity,
            set_midi_output_only: proxy.set_midi_output_only,
            set_drum_channel_muted: proxy.set_drum_channel_muted,
            set_equalizer_enabled: proxy.set_equalizer_enabled,
            set_min_velocity: proxy.set_min_velocity,
            set_max_note_on_time: proxy.set_max_note_on_time,
            set_slot_mode: proxy.set_slot_mode,
            set_primary_channel: proxy.set_primary_channel,
            clear_program_on_channel: proxy.clear_program_on_channel,
            synth_set_auto_fill_channels_with_default: proxy.synth_set_auto_fill_channels_with_default,
            set_use_default_instrument_when_missing_for_other_users: proxy.set_use_default_instrument_when_missing_for_other_users,
            resume_midi_file: proxy.resume_midi_file,
            pause_midi_file: proxy.pause_midi_file,
            stop_midi_file: proxy.stop_midi_file,
            load_midi_file_by_path: proxy.load_midi_file_by_path,
            load_midi_file: proxy.load_midi_file,
            set_octave_offset: proxy.set_octave_offset,
            set_transpose_offset: proxy.set_transpose_offset,
            list_midi_input_connections: core_wasm.list_midi_input_connections,
            list_midi_output_connections: core_wasm.list_midi_output_connections,
            open_midi_output_connection: core_wasm.open_midi_output_connection,
            open_midi_input_connection: core_wasm.open_midi_input_connection,
            close_midi_input_connection: core_wasm.close_midi_input_connection,
            close_midi_output_connection: core_wasm.close_midi_output_connection,
            emit_to_midi_output: proxy.emit_to_midi_output,
            note_on: proxy.note_on,
            note_off: proxy.note_off,
            set_client_socket_id: proxy.set_client_socket_id,
            from_socket_note_on: proxy.from_socket_note_on,
            from_socket_note_off: proxy.from_socket_note_off,
            from_socket_pitch: proxy.from_socket_pitch,
            set_channel_active: proxy.set_channel_active,
            synth_set_chorus: proxy.synth_set_chorus,
            synth_set_reverb: proxy.synth_set_reverb,
            set_apply_velocity_curve: proxy.set_apply_velocity_curve,
            volume_change: proxy.volume_change,
            pan_change: proxy.pan_change,
            mute_user: proxy.mute_user,
            load_soundfont: proxy.load_soundfont,
            synth_set_gain: proxy.synth_set_gain,
            synth_set_user_gain: proxy.synth_set_user_gain,
            synth_set_polyphony: proxy.synth_set_polyphony,
            synth_set_sample_rate: proxy.synth_set_sample_rate,
            set_disable_velocity_for_client: proxy.set_disable_velocity_for_client,
            program_select: proxy.program_select,
            bank_select: proxy.bank_select,
            damper_pedal: proxy.damper_pedal,
            all_notes_off: proxy.all_notes_off,
            all_sounds_off: proxy.all_sounds_off,
            reset_all_controllers: proxy.reset_all_controllers,
            parse_midi_data: proxy.parse_midi_data,
            synth_ws_socket_note_on: core_wasm.synth_ws_socket_note_on,
            synth_ws_socket_pitch: core_wasm.synth_ws_socket_pitch,
            // Executes on main thread, instead of worker thread
            parse_midi_data_non_proxy: core_wasm.parse_midi_data,
            dispose: proxy.dispose,
            disconnect: proxy.disconnect,
            reset: proxy.reset,
            synth_set_reverb_width: proxy.synth_set_reverb_width,
            synth_set_reverb_level: proxy.synth_set_reverb_level,
            synth_set_reverb_room_size: proxy.synth_set_reverb_room_size,
            synth_set_reverb_damp: proxy.synth_set_reverb_damp,
            add_socket: proxy.add_socket,
            remove_socket: proxy.remove_socket,
            bypassall_equalizer: proxy.bypassall_equalizer,
            reset_equalizer: proxy.reset_equalizer,
            set_equalizer_resonance: proxy.set_equalizer_resonance,
            set_equalizer_bypass: proxy.set_equalizer_bypass,
            set_equalizer_freq: proxy.set_equalizer_freq,
            set_equalizer_gain: proxy.set_equalizer_gain,
            set_equalizer_band: proxy.set_equalizer_band,
            get_synth_audio_channel: proxy.get_synth_audio_channel,
            set_user_volume: proxy.set_user_volume,
            set_user_velocity_percentage: proxy.set_user_velocity_percentage,
            get_synth_audio_channels: proxy.get_synth_audio_channels
          } as CoreService);

          sendInitializeAppState();
          setCoreWasmLoaded(true);
          resolve();
        }

        if (data.event === "error") {
          reject(data?.err ?? new Error("Failed to load WASM module"));
        }
      };

      worker.postMessage({ event: "load-wasm-module" });
      setCanvasWorker(worker as any);
    });
  };

  /**
   * Loads the core WebAssembly module for desktop.
   *
   * @returns {Promise<void>} A promise that resolves when the core module is loaded and initialized.
   */
  const loadCoreWasmDesktop = async (tauriService: ITauriService): Promise<void> => {
    if (coreService() != null) return;

    await tauriService.listenEvents(
      onHandleAppEvent,
      onHandleAppEffects
    );

    setCoreService(tauriService.createCoreService(
      createAppAction,
      coreService()?.emit_core_app_actions
    ));
  };

  /**
   * Loads the core WebAssembly module and initializes the necessary components.
   * If the core WebAssembly module is already loaded, this function does nothing.
   *
   * @returns {Promise<void>} A promise that resolves when the core WebAssembly module is loaded and initialized.
   */
  const loadCoreWasm = async (
    coreWasmService: ICoreWasmService,
    tauriService: ITauriService,
    forceMainThread = false
  ): Promise<void> => {
    if (coreWasmLoaded()) return;

    // @ts-ignore - Used for midi player loop
    self["performanceNow"] = () => performance.now();

    if (COMMON.IS_PRODUCTION || COMMON.IS_DESKTOP_APP && COMMON.IS_DEV_MODE) {
      window.removeEventListener("beforeunload", beforeUnload);
      window.addEventListener("beforeunload", beforeUnload);
    }

    webCore2RendererChannel = new BroadcastChannel(CHANNELS.PIANORHYTH_CORE_TO_RENDERER);

    if (COMMON.IS_WEB_APP) {
      if (forceMainThread) {
        await loadCoreWasmWebNonWebWorker(coreWasmService);
      } else {
        if (canCreateSharedArrayBuffer()) {
          await loadCoreWasmWeb(coreWasmService);
        } else {
          await loadCoreWasmWebNonWebWorker(coreWasmService);
        }
      }
    }

    if (COMMON.IS_DESKTOP_APP) await loadCoreWasmDesktop(tauriService);

    if (COMMON.IS_DESKTOP_APP) {
      let coreVersion = await coreService()?.get_core_version();
      let synthVersion = await coreService()?.get_synth_version();
      let rendererVersion = await coreService()?.get_renderer_version();

      logInfo(coreVersion);
      logInfo(synthVersion);
      logInfo(rendererVersion);
    }
    // else {
    //   let bgColor = "background: #000; color: #fff";
    //   console.log(`%c${coreVersion}`, bgColor);
    //   console.log(`%c${synthVersion}`, bgColor);
    //   console.log(`%c${rendererVersion}`, bgColor);
    // }

    setCoreWasmLoaded(true);

    if (COMMON.IS_AUTOMATED_TEST_MODE) {
      (window as any)["coreService"] = coreService;
    }
  };

  /**
   * Callback function called when a disconnect event occurs.
   */
  const onDisconnect = debounce(() => {
    if (COMMON.IS_DEV_MODE) console.log("Disconnecting...");

    // Hide any potential persistent notifications
    toast.remove(IDS.LOADING_SOUNDFONT);

    onDisconnectEvents.emit();
    setQueuedEffectsForRendererConsumed(false);
    setClient(new UserClientDomain());
    _setClientLoaded(false);
    coreService()?.send_app_action(
      AppStateActions.create({ action: AppStateActions_Action.Logout })
    );
  });

  /**
   * Sends an app action to set the value of isMobile.
   *
   * @param isMobile - A boolean value indicating whether the app is running on a mobile device.
   */
  const sendIsMobileAppAction = (isMobile: boolean) => {
    coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.SetIsMobile,
      boolValue: isMobile
    }));
  };

  function sendAppActionsToRenderer(actionBytes: Uint8Array) {
    webCore2RendererChannel?.postMessage({ event: "app_actions", data: actionBytes });
  }

  function getRoomSettingsToRoomParam() {
    let _roomSettings = roomSettings();

    if (_roomSettings) {
      if (roomType() == null || roomOwner() == null || roomName() == null) return null;

      let settings: CreateRoomParam = {
        ...DEFAULT_CREATE_ROOM_PARAM,
        RoomID: roomID(),
        RoomName: roomName()!,
        RoomType: roomType()!,
        RoomOwner: roomOwner()!,
        RoomStatus: _roomSettings.RoomStatus,
        Password: _roomSettings.Password,
        MaxPlayers: _roomSettings.MaxPlayers,
        WelcomeMessage: _roomSettings.WelcomeMessage,
        OnlyOwnerCanChat: _roomSettings.OnlyOwnerCanChat,
        OnlyOwnerCanPlay: _roomSettings.OnlyOwnerCanPlay,
        AllowBlackMidi: _roomSettings.AllowBlackMidi,
        AllowGuests: _roomSettings.AllowGuests,
        AllowBots: _roomSettings.AllowBots,
        OnlyMods: _roomSettings.OnlyMods,
        FilterProfanity: _roomSettings.FilterProfanity,
        StageDetailsPROTO: _roomSettings.StageDetails ? RoomStageDetails.encode(_roomSettings.StageDetails).finish() : new Uint8Array(),
        HostDetails: _roomSettings.HostDetails
      };

      return settings;
    }
  }

  /**
   * Updates the room details by applying a transformation function to the current room stage details.
   *
   * @param input - A function that takes the current `RoomStageDetails` and returns the updated `RoomStageDetails`.
   * @returns The updated room parameters with the transformed stage details, or `undefined` if the room parameters are not available.
   */
  function updateRoomDetails(input: (d: RoomStageDetails) => RoomStageDetails) {
    let roomParam = getRoomSettingsToRoomParam();

    if (roomParam) {
      let details: RoomStageDetails | undefined = undefined;

      // try { details = JSON.parse(roomParam.StageDetailsJSON || "{}") as RoomStageDetails; } catch { }
      try {
        details = RoomStageDetails.decode(roomParam.StageDetailsPROTO);
      } catch {
      }

      if (!details) {
        details = { stage: RoomStagesNS.DEFAULT_STAGE, effects: {}, audioEffects: {} };
      }

      if (details) {
        details = input(details);
        roomParam = {
          ...roomParam,
          StageDetailsPROTO: RoomStageDetails.encode(details).finish()
        };

        return roomParam;
      }
    }
  }

  const clearClient = () => {
    setClient(new UserClientDomain());
    _setClientLoaded(false);
  };

  if (COMMON.IS_DEV_MODE) {
    (window as any)["setActivatePageLoader"] = setActivatePageLoader;

    if (COMMON.IS_AUTOMATED_TEST_MODE) {
      (window as any)["setCoreService"] = setCoreService;
      (window as any)["appStateEvents"] = appStateEvents;
      (window as any)["setWelcomeEvent"] = setWelcomeEvent;
      (window as any)["appStateEffects"] = appStateEffects;
    }
  }

  return {
    loadCoreWasm,
    clearClient,
    wasmMidiSequencerEffects,
    createAppAction,
    canvasWorker,
    sendIsMobileAppAction,
    isRoomCurrentPage,
    userGestureController,
    updateRoomDetails,
    canInteract,
    currentPage, setCurrentPage,
    initialized, setInitialized,
    coreService,
    firstTimeJoinRoom,
    setFirstTimeJoinRoom,
    currentRoomParam, setCurrentRoomParam,
    onDisconnect,
    offlineMode: () => false,
    welcomeEvent,
    appStateEffects,
    isClientInDoNotDisturb,
    appStateEvents,
    setCoreService,
    serverServiceDown, setServerServiceDown,
    onHandleAppEffects,
    onHandleAppEvent,
    sendInitializeAppState,
    clientLoaded,
    decodeAppEffect,
    encodeAppEffect,
    queuedEffectsForRendererConsumed, setQueuedEffectsForRendererConsumed,
    queuedEffectsForRenderer, setQueuedEffectsForRenderer,
    activatePageLoader, setActivatePageLoader,
    queuedEventsForRenderer, setQueuedEventsForRenderer,
    pagerLoaderAnimating, setPagerLoaderAnimating,
    activePageLoaderToolTip, setActivePageLoaderToolTip,
    setRoomOwner: onSetRoomOwner,
    setClientIsSelfNotesMuted,
    isUserRoomOwner,
    isClient,
    doesClientHaveSheetMusicFullAccess,
    doesClientHaveMidiMusicFullAccess,
    clientIsSelfNotesMuted,
    setClientIsSelfChatMuted,
    canReport,
    setUserNotificationSettings,
    userNotificationSettings,
    clientIsSelfChatMuted,
    isClientServerNotesMuted,
    appVersionMismatched,
    roomType,
    onDisconnectEvents,
    roomSettings,
    setRoomSettings: _setRoomSettings,
    roomName,
    setAppVersionMismatched,
    maintenanceModeActive,
    setMaintenanceModeActive,
    canvasLoaded, _setCanvasLoaded,
    roomID,
    coreWasmLoaded,
    isClientMember,
    isClientInRoom,
    isClientMod,
    isClientRoomOwner,
    isClientAdmin,
    isClientProMember,
    getClientStatus,
    getUserTag,
    getClientMetaDetails,
    getSocketID,
    client,
    roomOwner,
    is2DMode: () => sceneMode() == PianoRhythmSceneMode.TWO_D,
    sceneMode, setSceneMode,
    desktopSynthEvents,
    webMidiConnectionEvents,
    clientHasEveryoneElseMuted,
    onClientLoaded,
    setClientHasEveryoneElseMuted: (value: boolean) => {
      _setClientHasEveryoneElseMuted(value);
      coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.MuteEveryoneElse,
        boolValue: value
      }));
    }
  };
}