# `handle_ws_midi_message` Function Optimization

## Critical Performance Issue

The `handle_ws_midi_message` function is a **major bottleneck** causing the "slightly delayed notes" and "missing notes" issues. This function processes every incoming MIDI message from other players and schedules them for audio playback.

## Current Implementation Problems

### 1. Individual setTimeout Calls

<augment_code_snippet path="pianorhythm_core/core/src/midi/wasm_handle_ws_midi.rs" mode="EXCERPT">
````rust
if let Some(output) = handle_ws_midi_message(&message, state) {
    for (ms, on_emit) in output {
        let timeout = gloo_timers::callback::Timeout::new(ms as u32, move || on_emit());
        timeout.forget();
    }
}
````
</augment_code_snippet>

**Problem**: Each note creates its own timer, causing significant overhead when multiple notes arrive simultaneously.

### 2. Problematic Time Calculation

<augment_code_snippet path="pianorhythm_core/core/src/midi/mod.rs" mode="EXCERPT">
````rust
let mut t = message_time - current_audio_state.server_time_offset as f64 + 
    pianorhythm_shared::GLOBAL_TIME_OFFSET as f64 - now;

if t < 0. {
    t = pianorhythm_shared::GLOBAL_TIME_OFFSET as f64; // 1000ms fallback!
}

let mut ms = (t + delay).max(0.0);
ms = ms + (ms / 1000.0); // Adds unnecessary 0.1% delay
````
</augment_code_snippet>

**Problems**:
- **1000ms Fallback**: When timing fails, notes are delayed by 1 full second
- **Unnecessary Delay**: `ms + (ms / 1000.0)` adds extra latency for no clear reason
- **Complex Logic**: Multiple edge cases make timing unpredictable

### 3. Per-Note Processing Overhead

Each note in a buffer goes through:
- Individual time calculation
- Individual closure creation
- Individual timer scheduling
- Individual audio synthesis call

## Optimized Implementation

### Step 1: Simplified Time Calculation

```rust
pub fn calculate_note_timing_optimized(
    message_time: f64,
    server_time_offset: f64,
    current_time: f64
) -> f64 {
    // Simplified, reliable timing calculation
    let server_adjusted_time = message_time + server_time_offset;
    let delay = server_adjusted_time - current_time;
    
    // Clamp to reasonable bounds (0-5000ms)
    delay.max(0.0).min(5000.0)
}
```

**Benefits**:
- Eliminates 1000ms fallback
- Removes unnecessary delay additions
- Predictable timing behavior
- Bounds checking prevents extreme delays

### Step 2: Batch Note Processing

```rust
#[derive(Debug, Clone)]
pub struct ScheduledNote {
    pub delay_ms: f64,
    pub note_data: MidiDto,
    pub note_source: NoteSourceType,
    pub socket_id_hash: u32,
}

#[derive(Debug)]
pub struct BatchedNoteSchedule {
    pub notes: Vec<ScheduledNote>,
    pub base_time: f64,
}

pub fn handle_ws_midi_message_optimized(
    message: &MidiMessageOutputDto, 
    state: Rc<AppState>
) -> Option<BatchedNoteSchedule> {
    let current_audio_state = &state.audio_process_state;

    if current_audio_state.muted_everyone_else {
        return None;
    }

    let midi_message = message.clone();
    let message_socket_id = midi_message.get_socketID();

    if message_socket_id.is_empty() {
        return None;
    }

    let socket_id_hash = hash_socket_id(&message_socket_id);

    #[cfg(feature = "use_synth")]
    if !current_audio_state.muted_users.contains(&message_socket_id.to_lowercase()) {
        let now = chrono::Utc::now().timestamp_millis() as f64;
        let message_time = midi_message.get_time().parse::<f64>().unwrap_or(now);
        
        // Use optimized timing calculation
        let base_delay = calculate_note_timing_optimized(
            message_time,
            current_audio_state.server_time_offset as f64,
            now
        );
        
        let mut batched_notes = Vec::new();
        
        for buffer in midi_message.get_data().into_iter().filter(|buffer| buffer.data.is_some()) {
            let note_delay = buffer.get_delay().min(1000.0);
            let total_delay = base_delay + note_delay;
            
            // Skip notes scheduled too far in the future
            if total_delay > 5000.0 {
                continue;
            }
            
            let buffer_data = buffer.get_data().to_owned();
            let note_source = NoteSourceType::from_proto_source(buffer_data.get_noteSource());
            
            batched_notes.push(ScheduledNote {
                delay_ms: total_delay,
                note_data: buffer_data,
                note_source,
                socket_id_hash,
            });
        }
        
        if !batched_notes.is_empty() {
            return Some(BatchedNoteSchedule {
                notes: batched_notes,
                base_time: now,
            });
        }
    }

    None
}
```

### Step 3: Optimized Scheduling

```rust
impl<'c> WasmHandleMidiMessage<'c> {
    fn handle(&self, message: &MidiMessageOutputDto, state: Rc<AppState>) -> () {
        if let Some(batch) = handle_ws_midi_message_optimized(&message, state) {
            self.schedule_note_batch_optimized(batch);
        }
    }
    
    fn schedule_note_batch_optimized(&self, batch: BatchedNoteSchedule) {
        // Sort notes by delay for optimal scheduling
        let mut sorted_notes = batch.notes;
        sorted_notes.sort_by(|a, b| a.delay_ms.partial_cmp(&b.delay_ms).unwrap());
        
        // Group notes by similar timing (within 5ms) for batch execution
        let mut note_groups = Vec::new();
        let mut current_group = Vec::new();
        let mut current_delay = 0.0;
        let grouping_threshold = 5.0; // 5ms grouping window
        
        for note in sorted_notes {
            if current_group.is_empty() || (note.delay_ms - current_delay).abs() < grouping_threshold {
                current_group.push(note);
                if current_group.len() == 1 {
                    current_delay = note.delay_ms;
                }
            } else {
                // Start new group
                note_groups.push((current_delay, std::mem::take(&mut current_group)));
                current_group.push(note);
                current_delay = note.delay_ms;
            }
        }
        
        // Don't forget the last group
        if !current_group.is_empty() {
            note_groups.push((current_delay, current_group));
        }
        
        // Schedule each group with a single timer
        for (delay, notes) in note_groups {
            let delay_ms = delay.max(0.0) as u32;
            
            let timeout = gloo_timers::callback::Timeout::new(delay_ms, move || {
                // Execute all notes in this group simultaneously
                for note in notes {
                    Self::execute_note_optimized(note);
                }
            });
            timeout.forget();
        }
    }
    
    fn execute_note_optimized(note: ScheduledNote) {
        match note.note_data.messageType {
            MidiDtoType::NoteOn if note.note_data.has_noteOn() => {
                let value = note.note_data.get_noteOn();
                let event = PianoRhythmWebSocketMidiNoteOn {
                    channel: value.get_channel() as u8,
                    note: value.get_note() as u8,
                    velocity: value.get_velocity() as u8,
                    program: Some(value.get_program() as u8),
                    bank: Some(value.get_bank() as u32),
                    volume: Some(value.get_volume() as u8),
                    pan: Some(value.get_pan() as u8),
                    source: Some(note.note_source.to_u8()),
                    ..Default::default()
                };
                pianorhythm_synth::synth_ws_socket_note_on(event, note.socket_id_hash);
            }
            MidiDtoType::NoteOff if note.note_data.has_noteOff() => {
                let value = note.note_data.get_noteOff();
                _ = pianorhythm_synth::parse_midi_data(
                    &[pianorhythm_shared::midi::NOTE_OFF_BYTE + value.get_channel() as u8, 
                      value.get_note() as u8, 0],
                    note.socket_id_hash,
                    Some(note.note_source.to_u8()),
                    None
                );
            }
            MidiDtoType::Sustain if note.note_data.has_sustain() => {
                let value = note.note_data.get_sustain();
                _ = pianorhythm_synth::parse_midi_data(
                    &[pianorhythm_shared::midi::CONTROLLER_BYTE, 64, if value.value { 64 } else { 0 }],
                    note.socket_id_hash,
                    Some(note.note_source.to_u8()),
                    None
                );
            }
            MidiDtoType::AllSoundOff => if note.note_data.has_allSoundOff() {
                _ = pianorhythm_synth::parse_midi_data(
                    &[pianorhythm_shared::midi::CONTROLLER_BYTE + note.note_data.get_allSoundOff().get_channel() as u8, 0x78, 0],
                    note.socket_id_hash,
                    Some(note.note_source.to_u8()),
                    None
                );
            }
            MidiDtoType::PitchBend => if note.note_data.has_pitchBend() {
                let value = note.note_data.get_pitchBend();
                _ = pianorhythm_synth::synth_ws_socket_pitch(
                    PianoRhythmWebSocketMidiPitchBend {
                        channel: value.get_channel() as u8,
                        value: value.get_value()
                    },
                    note.socket_id_hash
                );
            }
            _ => {}
        }
    }
}
```

## Performance Improvements

### Before Optimization
- **Individual Timers**: 10 notes = 10 setTimeout calls
- **Timing Fallback**: 1000ms delay when calculation fails
- **Processing Overhead**: Repeated calculations for each note
- **Unpredictable Delays**: Complex edge case handling

### After Optimization
- **Batched Timers**: 10 notes = ~2-3 setTimeout calls (grouped by timing)
- **Reliable Timing**: Maximum 5000ms delay, no extreme fallbacks
- **Batch Processing**: Single calculation per message, multiple notes processed together
- **Predictable Behavior**: Simple, bounded timing logic

### Expected Performance Gains
- **60-80% Reduction** in setTimeout overhead
- **Elimination** of 1000ms fallback delays
- **25-50ms Improvement** in average note latency
- **Better Synchronization** for chord and rapid note sequences

## Implementation Steps

### Phase 1: Replace Timing Calculation (Week 1)
1. Implement `calculate_note_timing_optimized`
2. Replace existing timing logic
3. Test with various network conditions
4. Monitor for timing regressions

### Phase 2: Add Batch Processing (Week 2)
1. Implement `BatchedNoteSchedule` structure
2. Update `handle_ws_midi_message` to return batches
3. Test with high-intensity playing scenarios
4. Verify note ordering and timing accuracy

### Phase 3: Optimize Scheduling (Week 3)
1. Implement grouped timer scheduling
2. Add performance monitoring
3. Test with multiple concurrent players
4. Fine-tune grouping thresholds

### Phase 4: Integration and Testing (Week 4)
1. Integrate with note buffer engine optimizations
2. Comprehensive performance testing
3. User acceptance testing
4. Production deployment with monitoring

## Testing Strategy

### Unit Tests
- Timing calculation accuracy
- Note batching logic
- Group scheduling behavior
- Edge case handling

### Integration Tests
- End-to-end latency measurement
- Multi-player synchronization
- High-load performance
- Network condition simulation

### Performance Benchmarks
- Timer creation overhead
- Memory usage optimization
- CPU utilization improvement
- Latency distribution analysis

This optimization should significantly improve the user experience by reducing note latency and eliminating the extreme 1000ms delays that cause notes to "disappear" or arrive very late.
